from fastapi import APIRouter, HTTPException, Depends, Request
from typing import List, Dict, Any, Optional
from models import AdminLoginRequest, ConfigUpdateRequest
from auth import (generate_tokens, check_login_attempts, clear_failed_attempts, 
                  record_failed_attempt, active_sessions)
from stats import get_stats, reset_stats, save_stats, update_image_stats, update_video_stats
from image_manager import (get_image_list, get_image_file, delete_image,
                          batch_delete_images, manual_cleanup)
from video_manager import (get_video_file, delete_video, batch_delete_videos,
                          manual_cleanup_videos, list_videos, get_video_stats)
from prompt_manager import PromptManager, CharacterManager
import secrets

router = APIRouter(prefix="/admin", tags=["admin"])

def create_admin_routes(config_getter, verify_admin_token):
    """创建管理员路由的工厂函数"""
    
    # 初始化管理器
    prompt_manager = PromptManager(config_getter)
    character_manager = CharacterManager(config_getter)
    
    @router.post("/login")
    async def admin_login(request: AdminLoginRequest, req: Request):
        """管理员登录"""
        config = config_getter()
        ip_address = req.client.host if req.client else "unknown"
        
        # 检查登录尝试次数
        if not check_login_attempts(ip_address, config):
            raise HTTPException(status_code=429, detail="登录尝试次数过多，请稍后再试")
        
        # 验证密码
        if request.password != config.get('admin_password'):
            record_failed_attempt(ip_address)
            raise HTTPException(status_code=401, detail="密码错误")
        
        # 清除失败尝试记录
        clear_failed_attempts(ip_address)
        
        # 生成令牌
        tokens = generate_tokens("admin", ip_address, config)
        
        return {"success": True, **tokens}

    @router.post("/logout")
    async def admin_logout(req: Request, authorized: dict = Depends(verify_admin_token)):
        """退出登录"""
        session_id = authorized.get('session_id')
        if session_id in active_sessions:
            del active_sessions[session_id]
        
        return {"success": True, "message": "已安全退出"}

    @router.get("/sessions")
    async def get_sessions(authorized: dict = Depends(verify_admin_token)):
        """获取活跃会话列表"""
        sessions = []
        for session_id, session_info in active_sessions.items():
            sessions.append({
                "session_id": session_id,
                "created_at": session_info['created_at'].isoformat(),
                "last_used": session_info['last_used'].isoformat(),
                "ip": session_info['ip']
            })
        
        return {"sessions": sessions}

    @router.delete("/sessions/{session_id}")
    async def revoke_session(session_id: str, authorized: dict = Depends(verify_admin_token)):
        """撤销指定会话"""
        if session_id in active_sessions:
            del active_sessions[session_id]
            return {"success": True, "message": "会话已撤销"}
        else:
            raise HTTPException(status_code=404, detail="会话不存在")

    @router.get("/config")
    async def get_config(authorized: dict = Depends(verify_admin_token)):
        """获取配置"""
        from config import load_config
        
        # 从文件重新加载最新配置
        config = load_config()
        
        safe_config = config.copy()
        # 隐藏敏感信息
        if 'review_api' in safe_config:
            safe_config['review_api'] = {**safe_config['review_api'], 'api_key': '*' * 10}
        if 'main_api' in safe_config:
            safe_config['main_api'] = {**safe_config['main_api'], 'api_key': '*' * 10}
        if 'jwt_config' in safe_config:
            safe_config['jwt_config'] = {**safe_config['jwt_config'], 'secret_key': '*' * 10}
        
        # API配置特殊处理
        if 'api_config' in safe_config:
            api_config = safe_config['api_config'].copy()
            api_config['api_key_set'] = bool(safe_config['api_config'].get('api_key'))
            api_config['api_key'] = ''  # 不返回实际值
            safe_config['api_config'] = api_config
        
        return safe_config

    @router.post("/config")
    async def update_config(request: ConfigUpdateRequest, authorized: dict = Depends(verify_admin_token)):
        """更新配置"""
        from config import save_config, validate_config
        
        config = config_getter()
        new_config = request.config
        
        # 保留现有的API密钥（如果新配置中是星号）
        if 'review_api' in new_config and new_config['review_api'].get('api_key') == '*' * 10:
            new_config['review_api']['api_key'] = config.get('review_api', {}).get('api_key', '')
        if 'main_api' in new_config and new_config['main_api'].get('api_key') == '*' * 10:
            new_config['main_api']['api_key'] = config.get('main_api', {}).get('api_key', '')
        if 'jwt_config' in new_config and new_config['jwt_config'].get('secret_key') == '*' * 10:
            new_config['jwt_config']['secret_key'] = config.get('jwt_config', {}).get('secret_key', '')
        if 'api_config' in new_config and new_config['api_config'].get('api_key') == '*' * 10:
            new_config['api_config']['api_key'] = config.get('api_config', {}).get('api_key', '')
        
        # 修复客户端配置处理逻辑
        if 'client_config' in new_config:
            # 确保保留现有的客户端配置
            if 'client_config' not in config:
                config['client_config'] = {}
            
            # 合并客户端配置，而不是覆盖
            current_client_config = config.get('client_config', {})
            updated_client_config = new_config['client_config']
            
            # 处理密码字段 - 如果新密码包含星号或为空，保留现有密码
            if ('password' not in updated_client_config or 
                not updated_client_config.get('password') or 
                '*' in str(updated_client_config.get('password', ''))):
                updated_client_config['password'] = current_client_config.get('password', 'virtual-love')
            
            # 合并其他配置
            current_client_config.update(updated_client_config)
            new_config['client_config'] = current_client_config
        else:
            # 如果没有传递客户端配置，保留现有配置
            if 'client_config' in config:
                new_config['client_config'] = config['client_config']
        
        config.update(new_config)
        save_config(config)
        
        # 保存后验证配置完整性
        validate_config()
        
        return {"success": True, "message": "配置更新成功"}

    @router.get("/stats")
    async def get_admin_stats(authorized: dict = Depends(verify_admin_token)):
        """获取统计数据"""
        from stats import load_stats
        load_stats()
        return get_stats()

    @router.post("/reset-stats")
    async def reset_admin_stats(authorized: dict = Depends(verify_admin_token)):
        """重置统计数据"""
        reset_stats()
        save_stats()
        return {"success": True, "message": "统计数据已重置"}

    @router.get("/current-client-password")
    async def get_current_client_password(authorized: dict = Depends(verify_admin_token)):
        """获取当前客户端密码（明文）"""
        config = config_getter()
        client_password = config.get('client_config', {}).get('password', 'virtual-love')
        return {"password": client_password}

    @router.get("/current-api-key")
    async def get_current_api_key(authorized: dict = Depends(verify_admin_token)):
        """获取当前API密钥（明文）"""
        config = config_getter()
        api_key = config.get('api_config', {}).get('api_key', '')
        return {"api_key": api_key}

    @router.post("/generate-api-key")
    async def generate_api_key(authorized: dict = Depends(verify_admin_token)):
        """生成新的API密钥"""
        from config import save_config, validate_config
        
        config = config_getter()
        new_key = "sk-" + secrets.token_urlsafe(32)
        
        # 更新配置
        if 'api_config' not in config:
            config['api_config'] = {}
        config['api_config']['api_key'] = new_key
        save_config(config)
        validate_config()
        
        return {"success": True, "api_key": new_key}

    # 图片管理相关路由
    @router.get("/images")
    async def get_uploaded_images(
        page: int = 1,
        limit: int = 20,
        authorized: dict = Depends(verify_admin_token)
    ):
        """管理员获取上传的图片列表"""
        config = config_getter()
        return get_image_list(page, limit, config)

    @router.get("/images/{filename}")
    async def get_image_file_admin(
        filename: str,
        format: str = "file",  # file 或 base64
        authorized: dict = Depends(verify_admin_token)
    ):
        """管理员获取具体图片文件"""
        config = config_getter()
        return get_image_file(filename, format, config)

    @router.delete("/images/{filename}")
    async def delete_image_file(
        filename: str,
        authorized: dict = Depends(verify_admin_token)
    ):
        """管理员删除图片文件"""
        config = config_getter()
        return delete_image(filename, config)

    @router.post("/images/batch-delete")
    async def batch_delete_images_admin(
        filenames: List[str],
        authorized: dict = Depends(verify_admin_token)
    ):
        """批量删除图片文件"""
        config = config_getter()
        return batch_delete_images(filenames, config)

    @router.post("/images/cleanup")
    async def manual_cleanup_images(authorized: dict = Depends(verify_admin_token)):
        """手动清理图片"""
        config = config_getter()
        return manual_cleanup(config)

    @router.get("/images/config")
    async def get_image_config(authorized: dict = Depends(verify_admin_token)):
        """获取图片管理配置"""
        config = config_getter()
        image_config = config.get('image_upload', {})
        return {
            "auto_cleanup": image_config.get('auto_cleanup', True),
            "cleanup_days": image_config.get('cleanup_days', 7),
            "max_storage_size_mb": image_config.get('max_storage_size', 500 * 1024 * 1024) // (1024 * 1024),
            "max_files": image_config.get('max_files', 1000),
            "max_file_size_mb": image_config.get('max_file_size', 10 * 1024 * 1024) // (1024 * 1024)
        }

    @router.post("/images/config")
    async def update_image_config(
        new_config: dict,
        authorized: dict = Depends(verify_admin_token)
    ):
        """更新图片管理配置"""
        from config import save_config, validate_config
        
        config = config_getter()
        
        if 'image_upload' not in config:
            config['image_upload'] = {}
        
        # 更新配置
        if 'auto_cleanup' in new_config:
            config['image_upload']['auto_cleanup'] = bool(new_config['auto_cleanup'])
        if 'cleanup_days' in new_config:
            config['image_upload']['cleanup_days'] = max(1, int(new_config['cleanup_days']))
        if 'max_storage_size_mb' in new_config:
            config['image_upload']['max_storage_size'] = max(1, int(new_config['max_storage_size_mb'])) * 1024 * 1024
        if 'max_files' in new_config:
            config['image_upload']['max_files'] = max(1, int(new_config['max_files']))
        if 'max_file_size_mb' in new_config:
            config['image_upload']['max_file_size'] = max(1, int(new_config['max_file_size_mb'])) * 1024 * 1024
        
        save_config(config)
        validate_config()
        
        return {
            "success": True,
            "message": "图片管理配置更新成功",
            "config": {
                "auto_cleanup": config['image_upload'].get('auto_cleanup', True),
                "cleanup_days": config['image_upload'].get('cleanup_days', 7),
                "max_storage_size_mb": config['image_upload'].get('max_storage_size', 500 * 1024 * 1024) // (1024 * 1024),
                "max_files": config['image_upload'].get('max_files', 1000),
                "max_file_size_mb": config['image_upload'].get('max_file_size', 10 * 1024 * 1024) // (1024 * 1024)
            }
        }

    # 视频管理相关路由
    @router.get("/videos")
    async def get_uploaded_videos(
        page: int = 1,
        limit: int = 20,
        authorized: dict = Depends(verify_admin_token)
    ):
        """管理员获取上传的视频列表"""
        config = config_getter()
        videos = list_videos(config)

        # 分页处理
        start = (page - 1) * limit
        end = start + limit
        paginated_videos = videos[start:end]

        return {
            "videos": paginated_videos,
            "total": len(videos),
            "page": page,
            "limit": limit,
            "total_pages": (len(videos) + limit - 1) // limit
        }

    @router.get("/videos/{filename}")
    async def get_video_file_admin(
        filename: str,
        format: str = "file",  # 只支持file格式
        authorized: dict = Depends(verify_admin_token)
    ):
        """管理员获取具体视频文件"""
        config = config_getter()
        return get_video_file(filename, format, config)

    @router.delete("/videos/{filename}")
    async def delete_video_file(
        filename: str,
        authorized: dict = Depends(verify_admin_token)
    ):
        """管理员删除视频文件"""
        config = config_getter()
        return delete_video(filename, config)

    @router.post("/videos/batch-delete")
    async def batch_delete_videos_admin(
        filenames: List[str],
        authorized: dict = Depends(verify_admin_token)
    ):
        """批量删除视频文件"""
        config = config_getter()
        return batch_delete_videos(filenames, config)

    @router.post("/videos/cleanup")
    async def manual_cleanup_videos_admin(authorized: dict = Depends(verify_admin_token)):
        """手动清理视频"""
        config = config_getter()
        return manual_cleanup_videos(config)

    @router.get("/videos/config")
    async def get_video_config(authorized: dict = Depends(verify_admin_token)):
        """获取视频管理配置"""
        config = config_getter()
        video_config = config.get('video_upload', {})
        return {
            "auto_cleanup": video_config.get('auto_cleanup', True),
            "cleanup_days": video_config.get('cleanup_days', 7),
            "max_storage_size_mb": video_config.get('max_storage_size', 5 * 1024 * 1024 * 1024) // (1024 * 1024),
            "max_files": video_config.get('max_files', 100),
            "max_file_size_mb": video_config.get('max_file_size', 500 * 1024 * 1024) // (1024 * 1024)
        }

    @router.post("/videos/config")
    async def update_video_config(
        new_config: dict,
        authorized: dict = Depends(verify_admin_token)
    ):
        """更新视频管理配置"""
        from config import save_config, validate_config

        config = config_getter()

        if 'video_upload' not in config:
            config['video_upload'] = {}

        # 更新配置
        if 'auto_cleanup' in new_config:
            config['video_upload']['auto_cleanup'] = bool(new_config['auto_cleanup'])
        if 'cleanup_days' in new_config:
            config['video_upload']['cleanup_days'] = int(new_config['cleanup_days'])
        if 'max_storage_size_mb' in new_config:
            config['video_upload']['max_storage_size'] = int(new_config['max_storage_size_mb']) * 1024 * 1024
        if 'max_files' in new_config:
            config['video_upload']['max_files'] = int(new_config['max_files'])
        if 'max_file_size_mb' in new_config:
            config['video_upload']['max_file_size'] = int(new_config['max_file_size_mb']) * 1024 * 1024

        save_config(config)
        validate_config()

        return {
            "success": True,
            "message": "视频管理配置更新成功",
            "config": {
                "auto_cleanup": config['video_upload'].get('auto_cleanup', True),
                "cleanup_days": config['video_upload'].get('cleanup_days', 7),
                "max_storage_size_mb": config['video_upload'].get('max_storage_size', 5 * 1024 * 1024 * 1024) // (1024 * 1024),
                "max_files": config['video_upload'].get('max_files', 100),
                "max_file_size_mb": config['video_upload'].get('max_file_size', 500 * 1024 * 1024) // (1024 * 1024)
            }
        }

    @router.get("/context-config")
    async def get_context_config(authorized: dict = Depends(verify_admin_token)):
        """获取上下文配置参数"""
        config = config_getter()
        from config import DEFAULT_CONFIG
        context_config = config.get('context_config', DEFAULT_CONFIG['context_config'])
        return context_config

    @router.post("/context-config")
    async def update_context_config(new_config: dict, authorized: dict = Depends(verify_admin_token)):
        """更新上下文配置参数"""
        from config import save_config, validate_config, DEFAULT_CONFIG
        
        config = config_getter()
        
        if 'context_config' not in config:
            config['context_config'] = DEFAULT_CONFIG['context_config'].copy()
        
        # 更新配置
        for key, value in new_config.items():
            config['context_config'][key] = value
        
        save_config(config)
        validate_config()
        return {"success": True, "message": "上下文配置更新成功", "config": config['context_config']}

    @router.post("/client-config")
    async def update_client_config(new_config: dict, authorized: dict = Depends(verify_admin_token)):
        """更新客户端配置"""
        from config import save_config, validate_config
        
        config = config_getter()
        
        # 如果密码是星号，保留现有密码
        if new_config.get('password') and '*' in new_config['password']:
            new_config['password'] = config.get('client_config', {}).get('password', 'chat2024')
        
        if 'client_config' not in config:
            config['client_config'] = {}
        
        config['client_config'].update(new_config)
        save_config(config)
        validate_config()
        
        return {"success": True, "message": "客户端配置更新成功"}

    # ========== 提示词管理API ==========
    
    @router.get("/prompts")
    async def get_all_prompts(authorized: dict = Depends(verify_admin_token)):
        """获取所有提示词"""
        try:
            prompts = prompt_manager.get_all_prompts()
            return {"success": True, "prompts": prompts}
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    @router.get("/prompts/{prompt_id}")
    async def get_prompt(prompt_id: str, authorized: dict = Depends(verify_admin_token)):
        """获取指定提示词"""
        try:
            prompt = prompt_manager.get_prompt(prompt_id)
            if not prompt:
                raise HTTPException(status_code=404, detail="提示词不存在")
            return {"success": True, "prompt": prompt}
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    @router.post("/prompts")
    async def create_prompt(prompt_data: Dict[str, Any], authorized: dict = Depends(verify_admin_token)):
        """创建新提示词"""
        try:
            prompt = prompt_manager.create_prompt(prompt_data)
            return {"success": True, "message": "提示词创建成功", "prompt": prompt}
        except ValueError as e:
            raise HTTPException(status_code=400, detail=str(e))
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    @router.put("/prompts/{prompt_id}")
    async def update_prompt(prompt_id: str, prompt_data: Dict[str, Any], authorized: dict = Depends(verify_admin_token)):
        """更新提示词"""
        try:
            prompt = prompt_manager.update_prompt(prompt_id, prompt_data)
            return {"success": True, "message": "提示词更新成功", "prompt": prompt}
        except ValueError as e:
            raise HTTPException(status_code=400, detail=str(e))
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    @router.delete("/prompts/{prompt_id}")
    async def delete_prompt(prompt_id: str, authorized: dict = Depends(verify_admin_token)):
        """删除提示词"""
        try:
            prompt_manager.delete_prompt(prompt_id)
            return {"success": True, "message": "提示词删除成功"}
        except ValueError as e:
            raise HTTPException(status_code=400, detail=str(e))
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    @router.post("/prompts/{prompt_id}/copy")
    async def copy_prompt(prompt_id: str, request_data: Dict[str, Any] = {}, authorized: dict = Depends(verify_admin_token)):
        """复制提示词"""
        try:
            new_name = request_data.get('new_name') if request_data else None
            prompt = prompt_manager.copy_prompt(prompt_id, new_name)
            return {"success": True, "message": "提示词复制成功", "prompt": prompt}
        except ValueError as e:
            raise HTTPException(status_code=400, detail=str(e))
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    @router.get("/prompts/{prompt_id}/export")
    async def export_prompt(prompt_id: str, authorized: dict = Depends(verify_admin_token)):
        """导出提示词"""
        try:
            export_data = prompt_manager.export_prompt(prompt_id)
            return {"success": True, "export_data": export_data}
        except ValueError as e:
            raise HTTPException(status_code=400, detail=str(e))
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    @router.get("/prompts/export/all")
    async def export_all_prompts(authorized: dict = Depends(verify_admin_token)):
        """导出所有提示词"""
        try:
            export_data = prompt_manager.export_all_prompts()
            return {"success": True, "export_data": export_data}
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    @router.post("/prompts/import")
    async def import_prompts(request_data: Dict[str, Any], authorized: dict = Depends(verify_admin_token)):
        """导入提示词"""
        try:
            import_data = request_data.get('import_data', {})
            overwrite = request_data.get('overwrite', False)
            results = prompt_manager.import_prompt(import_data, overwrite)
            
            # 构建消息
            if results['created'] > 0 or results['updated'] > 0:
                message_parts = []
                if results['created'] > 0:
                    message_parts.append(f"创建 {results['created']} 个提示词")
                if results['updated'] > 0:
                    message_parts.append(f"更新 {results['updated']} 个提示词")
                if results['skipped'] > 0:
                    message_parts.append(f"跳过 {results['skipped']} 个提示词")
                
                message = f"提示词导入完成！{', '.join(message_parts)}"
            else:
                message = "提示词导入完成，但没有处理任何有效数据"
            
            return {"success": True, "message": message, "results": results}
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    @router.get("/prompts/search")
    async def search_prompts(
        keyword: str = "",
        category: str = "",
        tags: str = "",
        authorized: dict = Depends(verify_admin_token)
    ):
        """搜索提示词"""
        try:
            tag_list = [tag.strip() for tag in tags.split(",") if tag.strip()] if tags else []
            results = prompt_manager.search_prompts(keyword, category, tag_list)
            return {"success": True, "results": results}
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    # ========== 角色管理API ==========
    
    @router.get("/characters")
    async def get_all_characters(authorized: dict = Depends(verify_admin_token)):
        """获取所有角色"""
        try:
            characters = character_manager.get_all_characters()
            return {"success": True, "characters": characters}
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    @router.get("/characters/active")
    async def get_active_characters(authorized: dict = Depends(verify_admin_token)):
        """获取活跃角色"""
        try:
            characters = character_manager.get_active_characters()
            return {"success": True, "characters": characters}
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    @router.get("/characters/{character_id}")
    async def get_character(character_id: str, authorized: dict = Depends(verify_admin_token)):
        """获取指定角色"""
        try:
            character = character_manager.get_character(character_id)
            if not character:
                raise HTTPException(status_code=404, detail="角色不存在")
            return {"success": True, "character": character}
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    @router.post("/characters")
    async def create_character(character_data: Dict[str, Any], authorized: dict = Depends(verify_admin_token)):
        """创建新角色"""
        try:
            character = character_manager.create_character(character_data)
            return {"success": True, "message": "角色创建成功", "character": character}
        except ValueError as e:
            raise HTTPException(status_code=400, detail=str(e))
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    @router.put("/characters/{character_id}")
    async def update_character(character_id: str, character_data: Dict[str, Any], authorized: dict = Depends(verify_admin_token)):
        """更新角色"""
        try:
            character = character_manager.update_character(character_id, character_data)
            return {"success": True, "message": "角色更新成功", "character": character}
        except ValueError as e:
            raise HTTPException(status_code=400, detail=str(e))
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    @router.delete("/characters/{character_id}")
    async def delete_character(character_id: str, authorized: dict = Depends(verify_admin_token)):
        """删除角色"""
        try:
            character_manager.delete_character(character_id)
            return {"success": True, "message": "角色删除成功"}
        except ValueError as e:
            raise HTTPException(status_code=400, detail=str(e))
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    @router.post("/characters/{character_id}/copy")
    async def copy_character(character_id: str, request_data: Dict[str, Any] = {}, authorized: dict = Depends(verify_admin_token)):
        """复制角色"""
        try:
            new_name = request_data.get('new_name') if request_data else None
            character = character_manager.copy_character(character_id, new_name)
            return {"success": True, "message": "角色复制成功", "character": character}
        except ValueError as e:
            raise HTTPException(status_code=400, detail=str(e))
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    @router.get("/characters/search")
    async def search_characters(
        keyword: str = "",
        category: str = "",
        tags: str = "",
        authorized: dict = Depends(verify_admin_token)
    ):
        """搜索角色"""
        try:
            tag_list = [tag.strip() for tag in tags.split(",") if tag.strip()] if tags else []
            results = character_manager.search_characters(keyword, category, tag_list)
            return {"success": True, "results": results}
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    @router.post("/characters/import")
    async def import_characters(import_data: Dict[str, Any], authorized: dict = Depends(verify_admin_token)):
        """批量导入角色"""
        try:
            characters_data = import_data.get('characters', [])
            overwrite = import_data.get('overwrite', False)
            
            if not characters_data:
                raise HTTPException(status_code=400, detail="导入数据为空")
            
            results = character_manager.import_characters(characters_data, overwrite=overwrite)
            return {"success": True, "message": "角色导入完成", "results": results}
        except ValueError as e:
            raise HTTPException(status_code=400, detail=str(e))
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    # ========== 角色计费管理API ==========
    
    @router.get("/characters/{character_id}/pricing")
    async def get_character_pricing(character_id: str, authorized: dict = Depends(verify_admin_token)):
        """获取角色计费配置"""
        try:
            pricing = character_manager.get_character_pricing(character_id)
            return {"success": True, "pricing": pricing}
        except ValueError as e:
            raise HTTPException(status_code=400, detail=str(e))
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    @router.put("/characters/{character_id}/pricing")
    async def update_character_pricing(character_id: str, pricing_data: Dict[str, Any], authorized: dict = Depends(verify_admin_token)):
        """更新角色计费配置"""
        try:
            character = character_manager.update_character_pricing(character_id, pricing_data)
            return {"success": True, "message": "角色计费配置更新成功", "character": character}
        except ValueError as e:
            raise HTTPException(status_code=400, detail=str(e))
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    @router.post("/characters/{character_id}/pricing/enable")
    async def enable_character_pricing(character_id: str, request_data: Dict[str, Any], authorized: dict = Depends(verify_admin_token)):
        """启用/禁用角色独立计费"""
        try:
            enable = request_data.get('enable', True)
            character = character_manager.enable_character_independent_pricing(character_id, enable)
            action = "启用" if enable else "禁用"
            return {"success": True, "message": f"已{action}角色独立计费", "character": character}
        except ValueError as e:
            raise HTTPException(status_code=400, detail=str(e))
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    @router.post("/characters/{character_id}/pricing/mode")
    async def set_character_pricing_mode(character_id: str, request_data: Dict[str, Any], authorized: dict = Depends(verify_admin_token)):
        """设置角色计费模式"""
        try:
            mode = request_data.get('mode', 'simple')
            character = character_manager.set_character_pricing_mode(character_id, mode)
            return {"success": True, "message": f"角色计费模式已设置为: {mode}", "character": character}
        except ValueError as e:
            raise HTTPException(status_code=400, detail=str(e))
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    @router.get("/characters/pricing/template")
    async def get_character_pricing_template(authorized: dict = Depends(verify_admin_token)):
        """获取角色计费配置模板"""
        try:
            template = character_manager.get_character_pricing_template()
            return {"success": True, "template": template}
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    @router.get("/characters/pricing/summary")
    async def get_characters_pricing_summary(authorized: dict = Depends(verify_admin_token)):
        """获取所有角色的计费配置摘要"""
        try:
            summary = character_manager.get_characters_pricing_summary()
            return {"success": True, "summary": summary}
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    @router.post("/characters/pricing/validate")
    async def validate_character_pricing(pricing_data: Dict[str, Any], authorized: dict = Depends(verify_admin_token)):
        """验证角色计费配置"""
        try:
            result = character_manager.validate_character_pricing(pricing_data)
            return {"success": True, "validation": result}
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    return router 