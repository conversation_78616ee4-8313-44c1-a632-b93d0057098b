<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频背景测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
        }

        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }

        .test-area {
            position: relative;
            width: 100%;
            height: 400px;
            border: 2px solid #333;
            border-radius: 8px;
            overflow: hidden;
            margin: 20px 0;
            background: #2a2a2a;
        }

        .test-area.has-video-background {
            position: relative;
        }

        .test-content {
            position: relative;
            z-index: 1;
            padding: 20px;
            background: rgba(0, 0, 0, 0.5);
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }

        .controls {
            margin: 20px 0;
        }

        .controls input {
            width: 400px;
            padding: 10px;
            margin: 5px;
            border: 1px solid #555;
            background: #333;
            color: white;
            border-radius: 4px;
        }

        .controls button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            background: #007acc;
            color: white;
            border-radius: 4px;
            cursor: pointer;
        }

        .controls button:hover {
            background: #005a9e;
        }

        .file-input {
            display: none;
        }

        .test-urls {
            margin: 20px 0;
            padding: 15px;
            background: #333;
            border-radius: 8px;
        }

        .test-urls h3 {
            margin-top: 0;
        }

        .test-urls p {
            margin: 5px 0;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎥 视频背景功能测试</h1>
        
        <div class="test-area" id="testArea">
            <div class="test-content">
                <h2>测试区域</h2>
                <p>这里会显示视频背景效果</p>
            </div>
        </div>

        <div class="controls">
            <h3>测试控制</h3>
            <input type="text" id="videoUrl" placeholder="输入视频URL或本地文件路径 (file:///)">
            <br>
            <button onclick="setVideoBackground()">设置视频背景</button>
            <button onclick="selectLocalFile()">选择本地文件</button>
            <button onclick="clearBackground()">清除背景</button>
            <input type="file" id="fileInput" class="file-input" accept="video/*,image/*" onchange="handleFileSelect(event)">
        </div>

        <div class="test-urls">
            <h3>测试用URL示例：</h3>
            <p><strong>在线视频：</strong></p>
            <p>https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4</p>
            <p>https://www.w3schools.com/html/mov_bbb.mp4</p>
            <p><strong>本地文件格式：</strong></p>
            <p>file:///C:/Users/<USER>/Videos/video.mp4</p>
            <p>file:///E:/视频/测试视频.mp4</p>
        </div>
    </div>

    <script>
        function isVideoUrl(url) {
            if (!url) return false;
            const videoExtensions = ['.mp4', '.webm', '.ogg', '.avi', '.mov', '.wmv', '.flv', '.mkv'];
            const urlLower = url.toLowerCase();
            
            const hasVideoExtension = videoExtensions.some(ext => urlLower.includes(ext));
            const hasVideoKeyword = urlLower.includes('video');
            const isLocalFile = urlLower.startsWith('file://') || urlLower.startsWith('file:///');
            const matchesVideoPattern = urlLower.match(/\.(mp4|webm|ogg|avi|mov|wmv|flv|mkv)(\?|#|$)/i);
            
            return hasVideoExtension || hasVideoKeyword || matchesVideoPattern || 
                   (isLocalFile && videoExtensions.some(ext => urlLower.includes(ext)));
        }

        function setVideoBackground() {
            const url = document.getElementById('videoUrl').value.trim();
            if (!url) {
                alert('请输入视频URL');
                return;
            }

            const testArea = document.getElementById('testArea');
            clearBackground();

            if (isVideoUrl(url)) {
                createVideoBackground(testArea, url);
            } else {
                createImageBackground(testArea, url);
            }
        }

        function createVideoBackground(container, videoUrl) {
            const video = document.createElement('video');
            video.id = 'test-background-video';
            video.src = videoUrl;
            video.autoplay = true;
            video.loop = true;
            video.muted = true;
            video.playsInline = true;
            
            video.style.cssText = `
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                object-fit: cover;
                z-index: 0;
                pointer-events: none;
            `;

            container.classList.add('has-video-background');
            container.insertBefore(video, container.firstChild);

            video.onerror = () => {
                alert('视频加载失败: ' + videoUrl);
                video.remove();
                container.classList.remove('has-video-background');
            };

            video.onloadeddata = () => {
                console.log('视频加载成功:', videoUrl);
            };
        }

        function createImageBackground(container, imageUrl) {
            container.style.backgroundImage = `url('${imageUrl}')`;
            container.style.backgroundSize = 'cover';
            container.style.backgroundPosition = 'center';
            container.style.backgroundRepeat = 'no-repeat';
        }

        function selectLocalFile() {
            document.getElementById('fileInput').click();
        }

        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (!file) return;

            const isImage = file.type.startsWith('image/');
            const isVideo = file.type.startsWith('video/');
            
            if (!isImage && !isVideo) {
                alert('请选择图片或视频文件');
                return;
            }

            const localUrl = URL.createObjectURL(file);
            document.getElementById('videoUrl').value = localUrl;
            
            const testArea = document.getElementById('testArea');
            clearBackground();

            if (isVideo) {
                createVideoBackground(testArea, localUrl);
            } else {
                createImageBackground(testArea, localUrl);
            }

            console.log('本地文件URL:', localUrl);
        }

        function clearBackground() {
            const testArea = document.getElementById('testArea');
            const existingVideo = document.getElementById('test-background-video');
            if (existingVideo) {
                existingVideo.remove();
            }
            testArea.classList.remove('has-video-background');
            testArea.style.backgroundImage = '';
        }

        // 页面加载时的提示
        window.onload = function() {
            console.log('视频背景测试页面已加载');
            console.log('支持的视频格式: MP4, WebM, OGG, AVI, MOV, WMV, FLV, MKV');
        };
    </script>
</body>
</html>
