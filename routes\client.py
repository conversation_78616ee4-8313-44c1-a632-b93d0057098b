import json
import os
import time
import uuid
import asyncio
import httpx
from fastapi import APIRouter, HTTPException, Depends, Request, UploadFile, File
from fastapi.responses import StreamingResponse
from models import ClientLoginRequest, ChatCompletionRequest, ChatMessage
from auth import (generate_tokens, check_login_attempts, clear_failed_attempts,
                  record_failed_attempt, active_sessions)
from stats import increment_total_requests, increment_successful_requests, increment_failed_requests
from image_manager import upload_image, get_image_file
from video_manager import upload_video, get_video_file
from utils import estimate_tokens, extract_text_from_content, calculate_conversation_cost, get_timestamp
from pydantic import BaseModel
from typing import List, Optional
from pathlib import Path
import secrets
import json
from datetime import datetime

router = APIRouter(prefix="/client", tags=["client"])

class SummaryRequest(BaseModel):
    model_config = {"protected_namespaces": ()}

    messages: List[dict]  # 要总结的消息列表
    model_id: Optional[str] = None  # 角色ID，用于获取角色名称
    summary_length: Optional[int] = 200  # 总结长度（字数）
    summary_style: Optional[str] = "detailed"  # 总结风格：brief/detailed/technical
    include_reasoning: Optional[bool] = False  # 是否包含推理过程
    custom_instructions: Optional[str] = ""  # 自定义总结指令

class RatingRequest(BaseModel):
    model_config = {"protected_namespaces": ()}

    message_id: str  # 消息ID
    model_id: str    # 模型ID
    rating_type: Optional[str] = None  # 'like', 'dislike', 或 null (取消)
    timestamp: str   # 时间戳



async def process_chat_request(chat_request, request, authorized, config_getter, original_request_body, is_responses_api=False):
    """通用的聊天请求处理函数"""
    from prompt_manager import CharacterManager, PromptManager

    config = config_getter()

    # 智能日志记录 - 避免打印巨大的图片数据
    log_request_body = original_request_body.copy()
    if 'messages' in log_request_body:
        for msg in log_request_body['messages']:
            if isinstance(msg.get('content'), list):
                for item in msg['content']:
                    if isinstance(item, dict) and item.get('type') == 'image_url':
                        if 'image_url' in item and 'url' in item['image_url']:
                            url = item['image_url']['url']
                            if len(url) > 100:  # 如果URL太长（通常是base64）
                                item['image_url']['url'] = f"{url[:50]}...[图片数据已截断,长度:{len(url)}]...{url[-20:]}"



    # 处理消息格式 - 确保兼容多媒体格式
    request_data = original_request_body.copy()
    if 'messages' in request_data:
        for message in request_data['messages']:
            # 如果content是数组，验证格式
            if isinstance(message.get('content'), list):
                for item in message['content']:
                    if isinstance(item, dict):
                        if item.get('type') == 'image_url':
                            # 验证图片URL格式
                            if not item.get('image_url', {}).get('url'):
                                raise HTTPException(
                                    status_code=400,
                                    detail="图片URL格式错误"
                                )

    # === 角色配置处理 ===
    character_manager = CharacterManager(config_getter)
    prompt_manager = PromptManager(config_getter)

    # 获取角色配置
    character = character_manager.get_character(chat_request.model)
    if not character:
        # 如果角色不存在，尝试使用默认角色
        character = character_manager.get_default_character()
        if not character:
            raise HTTPException(status_code=400, detail=f"角色 '{chat_request.model}' 不存在且没有默认角色")

    # 获取角色对应的提示词
    system_prompt_id = character.get('system_prompt_id')
    if system_prompt_id:
        prompt_data = prompt_manager.get_prompt(system_prompt_id)
        if prompt_data:
            # 添加或替换系统提示词（包括空内容的情况）
            system_message = ChatMessage(role="system", content=prompt_data.get('content', ''))

            # 检查是否已有系统消息，如果有就替换，否则插入到开头
            has_system = False
            for i, msg in enumerate(chat_request.messages):
                if msg.role == "system":
                    chat_request.messages[i] = system_message
                    has_system = True
                    break

            if not has_system:
                chat_request.messages.insert(0, system_message)
        else:
            print(f"[{get_timestamp()}] 未找到提示词ID '{system_prompt_id}' - 角色 '{character['name']}'")

    # 更新请求的模型为后端模型
    original_model = chat_request.model
    chat_request.model = character.get('backend_model', chat_request.model)

    # 意图审查
    if config.get('review_enabled', False):
        from api_proxy import review_content
        review_result = await review_content(chat_request.messages, config)

        if review_result.strip() != "可":
            from stats import increment_review_blocked, save_stats
            increment_review_blocked()
            save_stats()

            response_content = f"抱歉，我检测到您的请求可能涉及不当内容。请换个话题聊聊吧！😊"

            if chat_request.stream:
                async def blocked_stream():
                    try:
                        if is_responses_api:
                            # Responses API 格式的流式响应
                            yield f"event: response.created\n"
                            yield f"data: {json.dumps({'type': 'response.created', 'response': {'id': f'resp_{uuid.uuid4().hex}', 'status': 'completed'}}, ensure_ascii=False)}\n\n"

                            yield f"event: response.output_item.added\n"
                            yield f"data: {json.dumps({'type': 'response.output_item.added', 'item': {'type': 'message', 'role': 'assistant', 'content': [{'type': 'output_text', 'text': response_content}]}}, ensure_ascii=False)}\n\n"

                            yield f"event: response.completed\n"
                            yield f"data: {json.dumps({'type': 'response.completed'}, ensure_ascii=False)}\n\n"
                        else:
                            # Chat Completions 格式的流式响应
                            chunk_data = {
                                "id": f"chatcmpl-{uuid.uuid4().hex[:8]}",
                                "object": "chat.completion.chunk",
                                "created": int(time.time()),
                                "model": original_model,
                                "choices": [{
                                    "index": 0,
                                    "delta": {"content": response_content},
                                    "finish_reason": None
                                }]
                            }
                            yield f"data: {json.dumps(chunk_data, ensure_ascii=False)}\n\n"

                            end_chunk = {
                                "id": f"chatcmpl-{uuid.uuid4().hex[:8]}",
                                "object": "chat.completion.chunk",
                                "created": int(time.time()),
                                "model": original_model,
                                "choices": [{
                                    "index": 0,
                                    "delta": {},
                                    "finish_reason": "stop"
                                }]
                            }
                            yield f"data: {json.dumps(end_chunk, ensure_ascii=False)}\n\n"
                            yield "data: [DONE]\n\n"
                    except Exception:
                        pass  # 静默处理流式响应异常

                return StreamingResponse(
                    blocked_stream(),
                    media_type="text/event-stream",
                    headers={
                        "Cache-Control": "no-cache",
                        "Connection": "keep-alive",
                        "Access-Control-Allow-Origin": "*"
                    }
                )
            else:
                if is_responses_api:
                    # Responses API 格式的非流式响应
                    return {
                        "id": f"resp_{uuid.uuid4().hex}",
                        "object": "response",
                        "created": int(time.time()),
                        "status": "completed",
                        "model": original_model,
                        "output": [{
                            "type": "message",
                            "role": "assistant",
                            "content": [{
                                "type": "output_text",
                                "text": response_content
                            }]
                        }],
                        "usage": {
                            "input_tokens": 10,
                            "output_tokens": 20,
                            "total_tokens": 30
                        }
                    }
                else:
                    # Chat Completions 格式的非流式响应
                    return {
                        "id": f"chatcmpl-{uuid.uuid4().hex[:8]}",
                        "object": "chat.completion",
                        "created": int(time.time()),
                        "model": original_model,
                        "choices": [{
                            "index": 0,
                            "message": {
                                "role": "assistant",
                                "content": response_content
                            },
                            "finish_reason": "stop"
                        }],
                        "usage": {
                            "prompt_tokens": 10,
                            "completion_tokens": 20,
                            "total_tokens": 30
                        }
                    }

    # 提取对话条数限制参数
    context_history_turns = original_request_body.get('context_history_turns', None)

    # 获取会话信息
    session_id = authorized.get('session_id', 'unknown')
    user_identifier = f"session_{session_id}"
    client_ip = request.client.host if request.client else "unknown"
    conversation_id = original_request_body.get('conversation_id', session_id)

    # 代理请求到主API
    session_info = {
        'conversation_id': conversation_id,
        'user_identifier': user_identifier,
        'client_ip': client_ip
    }

    from api_proxy import proxy_openai_request
    result = await asyncio.wait_for(
        proxy_openai_request(chat_request, config, original_request_body, context_history_turns, original_model_id=original_model, session_info=session_info, is_responses_api=is_responses_api),
        timeout=600.0
    )

    if chat_request.stream:
        increment_successful_requests()
        from stats import save_stats
        save_stats()
        return result

    # 将返回的模型ID替换为原始角色ID
    if isinstance(result, dict) and 'model' in result:
        result['model'] = original_model



    increment_successful_requests()
    from stats import save_stats
    save_stats()

    return result

def create_client_routes(config_getter, verify_client_token, review_content, proxy_openai_request):
    """创建客户端路由的工厂函数"""

    @router.post("/login")
    async def client_login(request: ClientLoginRequest, req: Request):
        """客户端登录"""
        config = config_getter()
        ip_address = req.client.host if req.client else "unknown"

        # 检查登录尝试次数
        if not check_login_attempts(ip_address, config):
            raise HTTPException(status_code=429, detail="登录尝试次数过多，请稍后再试")

        # 验证密码
        client_password = config.get('client_config', {}).get('password', 'virtual-love')
        if request.password != client_password:
            record_failed_attempt(ip_address)
            raise HTTPException(status_code=401, detail="密码错误")

        # 清除失败尝试记录
        clear_failed_attempts(ip_address)

        # 生成客户端令牌（使用不同的用户标识）
        tokens = generate_tokens("client", ip_address, config)

        return {"success": True, **tokens}

    @router.get("/verify")
    async def verify_client_auth(req: Request, authorized: dict = Depends(verify_client_token)):
        """验证客户端令牌"""
        return {"valid": True, "user": authorized.get('sub')}

    @router.get("/models")
    async def client_list_models(req: Request, authorized: dict = Depends(verify_client_token)):
        """客户端专用模型列表接口（返回角色列表）"""
        from prompt_manager import CharacterManager

        try:
            # 使用角色管理器获取活跃角色
            character_manager = CharacterManager(config_getter)
            active_characters = character_manager.get_active_characters()

            models = []
            for character in active_characters:
                # 使用model_id作为用户面向的标识符
                model_id = character.get("model_id", character["id"])

                # 获取定价信息
                pricing_info = character.get("pricing", {})
                pricing_data = None

                if pricing_info.get("enable_independent_pricing", False):
                    # 角色有独立定价
                    pricing_mode = pricing_info.get("pricing_mode", "simple")

                    if pricing_mode == "simple":
                        simple_pricing = pricing_info.get("simple_pricing", {})
                        pricing_data = {
                            "input_price": simple_pricing.get("input_price_1m", 1.0),
                            "output_price": simple_pricing.get("output_price_1m", 5.0)
                        }
                    elif pricing_mode == "tiered":
                        tiered_pricing = pricing_info.get("tiered_pricing", {})
                        pricing_data = {
                            "input_price": tiered_pricing.get("input_price_1m", 1.0),
                            "output_price": tiered_pricing.get("output_price_1m", 5.0),
                            "input_price_high": tiered_pricing.get("input_price_1m_high", 2.0),
                            "output_price_high": tiered_pricing.get("output_price_1m_high", 8.0),
                            "threshold_tokens": tiered_pricing.get("threshold_tokens", 100000)
                        }
                else:
                    # 使用默认定价
                    default_template = character_manager.get_character_pricing_template()
                    simple_pricing = default_template.get("simple_pricing", {})
                    pricing_data = {
                        "input_price": simple_pricing.get("input_price_1m", 1.0),
                        "output_price": simple_pricing.get("output_price_1m", 5.0)
                    }

                # 构建模型数据
                model_data = {
                    "id": model_id,
                    "object": "model",
                    "created": int(time.time()),
                    "owned_by": "killerbest",
                    "permission": [],
                    "root": model_id,
                    "parent": None,
                    "display_name": character.get("display_name", character["name"]),
                    "description": character.get("description", ""),
                    "avatar_url": character.get("avatar_url", ""),
                    "category": character.get("category", ""),
                    "tags": character.get("tags", []),
                    "is_default": character.get("is_default", False),
                    "status": "enabled" if character.get("is_active", True) else "disabled"
                }

                # 添加定价信息
                if pricing_data:
                    model_data["pricing"] = pricing_data

                # 添加更新时间（如果有）
                if character.get("updated_at"):
                    model_data["last_updated"] = character.get("updated_at")

                models.append(model_data)

            return {
                "object": "list",
                "data": models
            }
        except Exception as e:
            import logging
            logging.error(f"获取角色列表失败: {e}", exc_info=True)
            # 降级到空列表
            return {
                "object": "list",
                "data": []
            }

    @router.post("/responses")
    async def client_responses(request: Request, authorized: dict = Depends(verify_client_token)):
        """客户端 Responses 接口 - 支持 OpenAI Responses API 格式"""
        from prompt_manager import CharacterManager, PromptManager

        config = config_getter()
        increment_total_requests()

        try:
            # 获取原始请求体
            original_request_body = await request.json()

            print(f"[CLIENT REQUEST - Responses] 模型: {original_request_body.get('model')}, 流式: {original_request_body.get('stream')}")

            # === 角色配置处理 ===
            character_manager = CharacterManager(config_getter)

            # 获取角色配置
            requested_model = original_request_body.get('model')
            character = character_manager.get_character(requested_model)
            if not character:
                # 如果角色不存在，尝试使用默认角色
                character = character_manager.get_default_character()
                if not character:
                    raise HTTPException(status_code=400, detail=f"角色 '{requested_model}' 不存在且没有默认角色")

            # 更新请求的模型为后端模型
            backend_model = character.get('backend_model', requested_model)
            original_request_body['model'] = backend_model

            # 处理 instructions 系统提示词
            prompt_manager = PromptManager(config_getter)
            system_prompt_id = character.get('system_prompt_id')
            if system_prompt_id:
                prompt_data = prompt_manager.get_prompt(system_prompt_id)
                if prompt_data:
                    # 如果角色有系统提示词，添加到 instructions
                    character_instructions = prompt_data.get('content', '')
                    if character_instructions.strip():
                        # 如果请求中已有 instructions，合并它们
                        existing_instructions = original_request_body.get('instructions', '')
                        if existing_instructions:
                            original_request_body['instructions'] = f"{character_instructions}\n\n{existing_instructions}"
                        else:
                            original_request_body['instructions'] = character_instructions

            # 获取会话信息
            session_id = authorized.get('session_id', 'unknown')
            user_identifier = f"session_{session_id}"
            client_ip = request.client.host if request.client else "unknown"
            conversation_id = original_request_body.get('conversation_id', session_id)

            # 代理请求到主API
            session_info = {
                'conversation_id': conversation_id,
                'user_identifier': user_identifier,
                'client_ip': client_ip
            }

            from api_proxy import proxy_responses_request
            result = await asyncio.wait_for(
                proxy_responses_request(original_request_body, config, session_info, requested_model),
                timeout=600.0
            )

            # 将返回的模型ID替换为原始角色ID
            if isinstance(result, dict) and 'model' in result:
                result['model'] = requested_model

            increment_successful_requests()
            from stats import save_stats
            save_stats()

            return result

        except Exception as e:
            increment_failed_requests()
            print(f"Responses API 错误: {str(e)}")
            raise HTTPException(status_code=500, detail=f"处理 Responses 请求时发生错误: {str(e)}")

    @router.post("/chat/completions")
    async def client_chat_completions(request: ChatCompletionRequest, req: Request, authorized: dict = Depends(verify_client_token)):
        """客户端专用聊天接口"""
        increment_total_requests()

        try:
            # 获取原始请求体
            original_request_body = await req.json()

            # 使用通用处理函数
            return await process_chat_request(request, req, authorized, config_getter, original_request_body, is_responses_api=False)

        except Exception as e:
            increment_failed_requests()
            print(f"Chat Completions API 错误: {str(e)}")
            raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")

    @router.post("/logout")
    async def client_logout(req: Request, authorized: dict = Depends(verify_client_token)):
        """客户端退出登录"""
        session_id = authorized.get('session_id')
        if session_id in active_sessions:
            del active_sessions[session_id]

        return {"success": True, "message": "已安全退出"}

    @router.get("/context-config")
    async def client_get_context_config(authorized: dict = Depends(verify_client_token)):
        """客户端获取上下文配置"""
        config = config_getter()
        from config import DEFAULT_CONFIG
        context_config = config.get('context_config', DEFAULT_CONFIG['context_config'])
        return context_config

    @router.post("/upload-image")
    async def client_upload_image(
        file: UploadFile = File(...),
        authorized: dict = Depends(verify_client_token)
    ):
        """客户端图片上传接口（云端存储模式）"""
        config = config_getter()
        return await upload_image(file, config)

    @router.get("/images/{image_id}")
    async def client_get_image_file(
        image_id: str,
        format: str = "file",  # file 或 base64 或 preview
        authorized: dict = Depends(verify_client_token)
    ):
        """客户端获取图片文件（支持新旧格式）"""
        from fastapi.responses import FileResponse
        config = config_getter()

        result = get_image_file(image_id, format, config)

        # 如果是文件格式，直接返回文件
        if format == "file":
            file_path = result.get("file_path")
            mime_type = result.get("mime_type", "image/jpeg")
            if file_path and os.path.exists(file_path):
                return FileResponse(
                    file_path,
                    media_type=mime_type,
                    filename=image_id
                )
            else:
                raise HTTPException(status_code=404, detail="图片文件不存在")

        # 其他格式返回JSON
        return result

    @router.post("/token-stats")
    async def calculate_token_stats(request: Request, authorized: dict = Depends(verify_client_token)):
        """计算对话的token统计信息和费用"""
        try:
            data = await request.json()
            messages = data.get("messages", [])
            model_id = data.get("model_id", "astra")  # 从请求中获取模型ID

            if not isinstance(messages, list):
                raise HTTPException(status_code=400, detail="消息必须是数组格式")

            total_tokens = 0
            total_reasoning_tokens = 0
            total_content_tokens = 0
            message_count = 0

            for msg in messages:
                if not isinstance(msg, dict) or "role" not in msg:
                    continue

                message_count += 1

                # 统计思考过程的token
                reasoning_text = msg.get("reasoning", "")
                if reasoning_text and isinstance(reasoning_text, str):
                    reasoning_tokens = estimate_tokens(reasoning_text)
                    total_reasoning_tokens += reasoning_tokens

                # 统计主要内容的token
                content = msg.get("content", "")
                content_text = extract_text_from_content(content) # 使用辅助函数提取文本
                content_tokens = estimate_tokens(content_text)
                total_content_tokens += content_tokens

            total_tokens = total_reasoning_tokens + total_content_tokens
            avg_tokens_per_message = round(total_tokens / max(1, message_count))

            # 计算费用信息
            cost_info = calculate_conversation_cost(messages, model_id)

            return {
                "success": True,
                "stats": {
                    "message_count": message_count,
                    "total_tokens": total_tokens,
                    "total_content_tokens": total_content_tokens,
                    "total_reasoning_tokens": total_reasoning_tokens,
                    "avg_tokens_per_message": avg_tokens_per_message,
                },
                "cost": cost_info
            }
        except Exception as e:
            import logging
            logging.error(f"Token统计错误: {str(e)}")
            return {
                "success": False,
                "error": "计算token统计信息时出错",
                "detail": str(e)
            }

    @router.post("/summary")
    async def create_summary(request: SummaryRequest, authorized: dict = Depends(verify_client_token)):
        """生成对话历史总结"""
        config = config_getter()
        summary_config = config.get('summary_api', {})

        if not summary_config.get('enabled', False):
            raise HTTPException(status_code=503, detail="总结功能未启用")

        try:
            # 验证消息格式
            if not request.messages or len(request.messages) == 0:
                raise HTTPException(status_code=400, detail="消息列表不能为空")

            # === 新增：动态获取角色信息 ===
            from prompt_manager import CharacterManager
            character_manager = CharacterManager(config_getter)

            # 从请求中获取模型ID，如果没有就使用默认
            model_id = request.model_id or "astra"  # 使用传入的角色ID或默认角色

            # 获取角色信息
            character = character_manager.get_character(model_id)
            if not character:
                character = character_manager.get_default_character()

            character_name = character.get('name', '助手') if character else '助手'
            character_display_name = character.get('display_name', character_name) if character else character_name

            # 构建总结提示词

            style_prompts = {
                "brief": "简洁地梳理",
                "detailed": "详细地整理",
                "technical": "从专业角度分析"
            }

            style_prompt = style_prompts.get(request.summary_style, "详细地整理")

            # 准备对话内容 - 使用真实角色名称
            conversation_text = ""
            for msg in request.messages:
                role = msg.get('role', '')
                content = extract_text_from_content(msg.get('content', ''))

                if role == 'user':
                    conversation_text += f"用户：{content}\n\n"
                elif role == 'assistant':
                    conversation_text += f"{character_display_name}：{content}\n\n"
                    # 如果包含推理过程
                    if request.include_reasoning and msg.get('reasoning'):
                        conversation_text += f"[{character_display_name}的内心想法]：{msg.get('reasoning')}\n\n"

            # 构建更自然的总结请求
            summary_prompt = f"""请{style_prompt}这段对话，控制在{request.summary_length}字以内。

{request.custom_instructions}

对话内容：
{conversation_text}

请从以下角度总结：
1. 对话的主要话题和内容脉络
2. 双方的情绪变化和互动特点
3. 重要的观点、决定或结论
4. 如果涉及角色扮演，请描述角色关系和重要情节
5. 整体对话的氛围和风格

注意：
- 这是两个真实的人在对话，请把{character_display_name}当作一个真实的人，而不是AI系统
- 用自然的语言描述，避免"AI回应"、"AI分析"等机械化表达
- 重点突出对话的人情味和真实感
- 字数控制在{request.summary_length}字以内

总结："""

            # 准备API请求 - 优化系统提示词
            summary_messages = [
                {"role": "system", "content": f"你是一个对话记录整理助手。你需要把对话总结得像真实的人与人之间的交流记录，避免提及任何AI、机器人、系统等技术术语。请把{character_display_name}理解为一个真实存在的人，专注于描述对话的内容和情感，而不是技术分析。"},
                {"role": "user", "content": summary_prompt}
            ]

            request_data = {
                "model": summary_config.get('model', 'gpt-4.1-mini'),
                "messages": summary_messages,
                "max_tokens": summary_config.get('max_tokens', 2048),
                "temperature": summary_config.get('temperature', 0.3),
                "stream": False
            }

            # 发送API请求 - 添加重试机制
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    # 增加连接和读取超时时间
                    timeout = httpx.Timeout(connect=30.0, read=60.0, write=30.0, pool=30.0)
                    async with httpx.AsyncClient(timeout=timeout, verify=False) as client:  # 临时禁用SSL验证
                        response = await client.post(
                            f"{summary_config['base_url']}/chat/completions",
                            headers={
                                "Authorization": f"Bearer {summary_config['api_key']}",
                                "Content-Type": "application/json"
                            },
                            json=request_data
                        )

                        if response.status_code != 200:
                            error_detail = f"总结API请求失败(状态码:{response.status_code})"
                            try:
                                error_text = response.text
                                if len(error_text) > 200:
                                    error_text = error_text[:200] + "..."
                                error_detail += f": {error_text}"
                            except:
                                pass
                            raise HTTPException(status_code=response.status_code, detail=error_detail)

                        result = response.json()
                        summary_content = result.get('choices', [{}])[0].get('message', {}).get('content', '')

                        if not summary_content:
                            raise HTTPException(status_code=500, detail="总结生成失败：返回内容为空")

                        # 统计token使用
                        usage = result.get('usage', {})

                        # 输出总结API统计信息
                        print(f"[SUMMARY STATS] 输入: {usage.get('prompt_tokens', 0)} tokens, 输出: {usage.get('completion_tokens', 0)} tokens, 总计: {usage.get('total_tokens', 0)} tokens")
                        print(f"[SUMMARY INFO] 总结字符数: {len(summary_content)}, 模型: {request_data['model']}, 角色: {character_display_name}")

                        return {
                            "success": True,
                            "summary": summary_content,
                            "usage": usage,
                            "config": {
                                "model": request_data['model'],
                                "character_name": character_display_name,
                                "length": request.summary_length,
                                "style": request.summary_style,
                                "include_reasoning": request.include_reasoning
                            }
                        }

                except (httpx.ConnectError, httpx.ConnectTimeout) as e:
                    if attempt < max_retries - 1:
                        print(f"[总结API] 连接失败，第{attempt + 1}次重试... 错误: {str(e)}")
                        await asyncio.sleep(2 ** attempt)  # 指数退避
                        continue
                    else:
                        raise HTTPException(
                            status_code=503,
                            detail=f"总结API连接失败，请检查网络连接或API服务状态。详细错误: {str(e)}"
                        )
                except httpx.TimeoutException as e:
                    if attempt < max_retries - 1:
                        print(f"[总结API] 请求超时，第{attempt + 1}次重试... 错误: {str(e)}")
                        await asyncio.sleep(1)
                        continue
                    else:
                        raise HTTPException(status_code=504, detail=f"总结请求超时，已重试{max_retries}次")

        except HTTPException:
            raise  # 重新抛出HTTP异常
        except Exception as e:
            import logging
            logging.error(f"总结生成错误: {str(e)}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"总结生成失败: {str(e)}")


    @router.post("/rate-message")
    async def rate_message(request: RatingRequest, authorized: dict = Depends(verify_client_token)):
        """处理消息点赞数据"""
        try:
            # 验证评分类型
            if request.rating_type and request.rating_type not in ['like', 'dislike']:
                raise HTTPException(status_code=400, detail="评分类型必须是 'like' 或 'dislike'")

            # 获取用户标识（使用session_id或IP）
            user_id = authorized.get('session_id', 'anonymous')

            # 读取现有的评分数据
            ratings_file = "ratings_data.json"
            if os.path.exists(ratings_file):
                with open(ratings_file, 'r', encoding='utf-8') as f:
                    ratings_data = json.load(f)
            else:
                ratings_data = {
                    "user_ratings": {},  # 用户评分记录 {user_id: {message_id: rating_type}}
                    "message_stats": {},  # 消息统计 {message_id: {likes: count, dislikes: count}}
                    "model_stats": {}     # 模型统计 {model_id: {likes: count, dislikes: count, total_score: score}}
                }

            # 确保数据结构完整
            for key in ["user_ratings", "message_stats", "model_stats"]:
                if key not in ratings_data:
                    ratings_data[key] = {}

            if user_id not in ratings_data["user_ratings"]:
                ratings_data["user_ratings"][user_id] = {}

            # 获取用户之前对该消息的评分
            previous_rating = ratings_data["user_ratings"][user_id].get(request.message_id)

            # 初始化消息和模型统计
            if request.message_id not in ratings_data["message_stats"]:
                ratings_data["message_stats"][request.message_id] = {"likes": 0, "dislikes": 0}

            if request.model_id not in ratings_data["model_stats"]:
                ratings_data["model_stats"][request.model_id] = {"likes": 0, "dislikes": 0, "total_score": 0}

            # 更新评分逻辑
            message_stats = ratings_data["message_stats"][request.message_id]
            model_stats = ratings_data["model_stats"][request.model_id]

            # 如果之前有评分，先撤销
            if previous_rating:
                if previous_rating == 'like':
                    message_stats["likes"] = max(0, message_stats["likes"] - 1)
                    model_stats["likes"] = max(0, model_stats["likes"] - 1)
                    model_stats["total_score"] -= 1
                elif previous_rating == 'dislike':
                    message_stats["dislikes"] = max(0, message_stats["dislikes"] - 1)
                    model_stats["dislikes"] = max(0, model_stats["dislikes"] - 1)
                    model_stats["total_score"] += 1

            # 如果不是取消操作，添加新评分
            if request.rating_type:
                if request.rating_type == 'like':
                    message_stats["likes"] += 1
                    model_stats["likes"] += 1
                    model_stats["total_score"] += 1
                elif request.rating_type == 'dislike':
                    message_stats["dislikes"] += 1
                    model_stats["dislikes"] += 1
                    model_stats["total_score"] -= 1

                # 更新用户评分记录
                ratings_data["user_ratings"][user_id][request.message_id] = request.rating_type
            else:
                # 取消评分，删除记录
                if request.message_id in ratings_data["user_ratings"][user_id]:
                    del ratings_data["user_ratings"][user_id][request.message_id]

            # 保存数据
            with open(ratings_file, 'w', encoding='utf-8') as f:
                json.dump(ratings_data, f, ensure_ascii=False, indent=2)

            return {
                "success": True,
                "message": "评分已保存",
                "message_stats": message_stats,
                "model_stats": model_stats
            }

        except Exception as e:
            import logging
            logging.error(f"保存评分数据失败: {str(e)}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"保存评分失败: {str(e)}")

    @router.get("/leaderboard")
    async def get_leaderboard(authorized: dict = Depends(verify_client_token)):
        """获取全局排行榜数据"""
        try:
            ratings_file = "ratings_data.json"
            if not os.path.exists(ratings_file):
                return []

            with open(ratings_file, 'r', encoding='utf-8') as f:
                ratings_data = json.load(f)

            model_stats = ratings_data.get("model_stats", {})

            # 获取模型显示名称
            from prompt_manager import CharacterManager
            character_manager = CharacterManager(config_getter)

            # 转换为排行榜格式
            leaderboard = []
            for model_id, stats in model_stats.items():
                # 获取模型显示名称
                character = character_manager.get_character(model_id)
                model_name = character.get('display_name', character.get('name', model_id)) if character else model_id

                leaderboard.append({
                    "model_id": model_id,
                    "model_name": model_name,
                    "likes": stats.get("likes", 0),
                    "dislikes": stats.get("dislikes", 0),
                    "total_score": stats.get("total_score", 0)
                })

            # 按总分排序
            leaderboard.sort(key=lambda x: x["total_score"], reverse=True)

            return leaderboard

        except Exception as e:
            import logging
            logging.error(f"获取排行榜数据失败: {str(e)}", exc_info=True)
            # 出错时返回空列表，不阻断功能
            return []

    @router.post("/conversations/share")
    async def share_conversations(request: Request, authorized: dict = Depends(verify_client_token)):
        """分享对话"""
        try:
            body = await request.json()
            conversations = body.get('conversations', [])

            if not conversations:
                return {"success": False, "error": "没有选择要分享的对话"}

            # 生成随机的分享ID
            share_id = secrets.token_urlsafe(12)

            # 创建分享数据
            share_data = {
                "share_id": share_id,
                "created_at": datetime.now().isoformat(),
                "conversations": conversations,
                "shared_by": authorized.get('session_id', 'anonymous'),
                "total_conversations": len(conversations)
            }

            # 创建按日期分类的目录
            today = datetime.now().strftime('%Y-%m-%d')
            share_dir = Path("shared_conversations") / today
            share_dir.mkdir(parents=True, exist_ok=True)

            # 保存分享文件
            share_file = share_dir / f"share_{share_id}.json"
            with open(share_file, 'w', encoding='utf-8') as f:
                json.dump(share_data, f, ensure_ascii=False, indent=2)

            return {
                "success": True,
                "share_id": share_id,
                "share_url": f"/import-shared/{share_id}",
                "total_conversations": len(conversations)
            }

        except Exception as e:
            import logging
            logging.error(f"分享对话失败: {str(e)}", exc_info=True)
            return {"success": False, "error": f"分享失败: {str(e)}"}

    @router.get("/conversations/shared/{share_id}")
    async def get_shared_conversations(share_id: str):
        """获取分享的对话"""
        try:
            # 搜索所有日期目录下的分享文件
            shared_dir = Path("shared_conversations")
            if not shared_dir.exists():
                return {"success": False, "error": "分享文件不存在"}

            # 遍历所有日期目录查找分享文件
            for date_dir in shared_dir.iterdir():
                if date_dir.is_dir():
                    share_file = date_dir / f"share_{share_id}.json"
                    if share_file.exists():
                        with open(share_file, 'r', encoding='utf-8') as f:
                            share_data = json.load(f)

                        return {
                            "success": True,
                            "conversations": share_data.get('conversations', []),
                            "created_at": share_data.get('created_at'),
                            "total_conversations": share_data.get('total_conversations', 0)
                        }

            return {"success": False, "error": "分享链接不存在或已过期"}

        except Exception as e:
            import logging
            logging.error(f"获取分享对话失败: {str(e)}", exc_info=True)
            return {"success": False, "error": f"获取失败: {str(e)}"}

    @router.post("/upload-video")
    async def client_upload_video(
        file: UploadFile = File(...),
        authorized: dict = Depends(verify_client_token)
    ):
        """客户端视频上传接口"""
        config = config_getter()
        return await upload_video(file, config)

    @router.get("/videos/{video_id}")
    async def client_get_video_file(
        video_id: str,
        format: str = "file",  # 只支持file格式
        authorized: dict = Depends(verify_client_token)
    ):
        """客户端获取视频文件"""
        config = config_getter()
        # get_video_file现在直接返回FileResponse
        return get_video_file(video_id, format, config)

    return router