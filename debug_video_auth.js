// 调试视频认证问题的脚本
// 在浏览器控制台中运行

console.log('🎬 开始调试视频认证问题...');

// 检查token
const token = localStorage.getItem('client_token');
console.log('Token:', token ? `${token.substring(0, 20)}...` : '未找到');

if (!token) {
    console.error('❌ 没有找到认证token，请先登录');
} else {
    // 测试视频URL认证
    const testVideoUrl = '/client/videos/1756456838198_2a8cfd50.mp4';
    
    console.log('🔍 测试视频URL认证...');
    
    fetch(testVideoUrl, {
        headers: {
            'Authorization': `Bearer ${token}`
        }
    })
    .then(response => {
        console.log('响应状态:', response.status);
        console.log('响应头:', Object.fromEntries(response.headers.entries()));
        
        if (response.ok) {
            console.log('✅ 视频认证成功');
            return response.blob();
        } else {
            console.error('❌ 视频认证失败');
            return response.text();
        }
    })
    .then(data => {
        if (data instanceof Blob) {
            const blobUrl = URL.createObjectURL(data);
            console.log('✅ Blob URL创建成功:', blobUrl);
            
            // 测试在video元素中使用
            const testVideo = document.createElement('video');
            testVideo.src = blobUrl;
            testVideo.style.cssText = 'position: fixed; top: 10px; right: 10px; width: 200px; height: 150px; z-index: 9999; border: 2px solid red;';
            testVideo.controls = true;
            testVideo.muted = true;
            document.body.appendChild(testVideo);
            
            console.log('🎥 测试视频元素已添加到页面右上角');
            
            testVideo.onloadeddata = () => {
                console.log('✅ 视频加载成功');
            };
            
            testVideo.onerror = (e) => {
                console.error('❌ 视频加载失败:', e);
            };
            
        } else {
            console.error('认证失败响应:', data);
        }
    })
    .catch(error => {
        console.error('❌ 请求失败:', error);
    });
}

// 检查chatApp对象
if (typeof chatApp !== 'undefined') {
    console.log('✅ chatApp对象存在');
    
    // 测试getAuthenticatedVideoUrl函数
    if (typeof chatApp.getAuthenticatedVideoUrl === 'function') {
        console.log('✅ getAuthenticatedVideoUrl函数存在');
        
        const testUrl = '/client/videos/1756456838198_2a8cfd50.mp4';
        chatApp.getAuthenticatedVideoUrl(testUrl)
            .then(blobUrl => {
                if (blobUrl) {
                    console.log('✅ getAuthenticatedVideoUrl成功:', blobUrl);
                } else {
                    console.error('❌ getAuthenticatedVideoUrl返回空值');
                }
            })
            .catch(error => {
                console.error('❌ getAuthenticatedVideoUrl失败:', error);
            });
    } else {
        console.error('❌ getAuthenticatedVideoUrl函数不存在');
    }
} else {
    console.error('❌ chatApp对象不存在');
}

console.log('🔧 调试脚本执行完成');
