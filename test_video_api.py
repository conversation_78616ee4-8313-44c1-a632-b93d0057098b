#!/usr/bin/env python3
"""
测试视频API的脚本
"""
import requests
import json
import os

# 配置
BASE_URL = "http://127.0.0.1:7766"
CLIENT_PASSWORD = "virtual-love"  # 默认客户端密码

def login():
    """登录获取token"""
    login_data = {
        "password": CLIENT_PASSWORD
    }
    
    response = requests.post(f"{BASE_URL}/client/login", json=login_data)
    if response.status_code == 200:
        result = response.json()
        if result.get("success"):
            return result.get("access_token")
    
    print(f"登录失败: {response.status_code} - {response.text}")
    return None

def test_video_upload(token):
    """测试视频上传"""
    # 创建一个小的测试视频文件（实际上是文本文件，但用于测试）
    test_file_content = b"fake video content for testing"
    
    files = {
        'file': ('test_video.mp4', test_file_content, 'video/mp4')
    }
    
    headers = {
        'Authorization': f'Bearer {token}'
    }
    
    response = requests.post(f"{BASE_URL}/client/upload-video", files=files, headers=headers)
    
    print(f"视频上传响应: {response.status_code}")
    print(f"响应内容: {response.text}")
    
    if response.status_code == 200:
        result = response.json()
        if result.get("success"):
            return result.get("video_url")
    
    return None

def test_video_access(token, video_url):
    """测试视频访问"""
    if not video_url or not video_url.startswith("video_ref:"):
        print("无效的视频URL")
        return False
    
    # 转换为实际URL
    video_id = video_url.replace("video_ref:", "")
    actual_url = f"/client/videos/{video_id}"
    
    headers = {
        'Authorization': f'Bearer {token}'
    }
    
    response = requests.get(f"{BASE_URL}{actual_url}", headers=headers)
    
    print(f"视频访问响应: {response.status_code}")
    print(f"Content-Type: {response.headers.get('content-type', 'N/A')}")
    
    return response.status_code == 200

def main():
    print("🎬 开始测试视频API...")
    
    # 1. 登录
    print("\n1. 登录...")
    token = login()
    if not token:
        print("❌ 登录失败，无法继续测试")
        return
    
    print(f"✅ 登录成功，Token: {token[:20]}...")
    
    # 2. 测试视频上传
    print("\n2. 测试视频上传...")
    video_url = test_video_upload(token)
    if not video_url:
        print("❌ 视频上传失败")
        return
    
    print(f"✅ 视频上传成功，URL: {video_url}")
    
    # 3. 测试视频访问
    print("\n3. 测试视频访问...")
    access_success = test_video_access(token, video_url)
    if access_success:
        print("✅ 视频访问成功")
    else:
        print("❌ 视频访问失败")
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    main()
