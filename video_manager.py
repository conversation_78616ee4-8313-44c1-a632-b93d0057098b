import os
import time
import secrets
import mimetypes
from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional, List, Dict, Any
from fastapi import HTTPEx<PERSON>, UploadFile
from config import get_timestamp
from stats import update_video_stats, save_stats

def ensure_video_upload_directory(config: Dict[str, Any]) -> Path:
    """确保视频上传目录存在"""
    upload_path = Path(config.get('video_upload', {}).get('local_storage_path', 'video_uploads'))
    upload_path.mkdir(exist_ok=True)
    return upload_path

def cleanup_old_videos(config: Dict[str, Any]):
    """清理旧视频文件"""
    try:
        video_config = config.get('video_upload', {})
        if not video_config.get('auto_cleanup', True):
            return
        
        upload_dir = ensure_video_upload_directory(config)
        cleanup_days = video_config.get('cleanup_days', 7)
        cutoff_time = time.time() - (cleanup_days * 24 * 60 * 60)
        
        deleted_count = 0
        deleted_size = 0
        
        for file_path in upload_dir.iterdir():
            if file_path.is_file():
                try:
                    if file_path.stat().st_mtime < cutoff_time:
                        file_size = file_path.stat().st_size
                        file_path.unlink()
                        deleted_count += 1
                        deleted_size += file_size
                        print(f"[{get_timestamp()}] 🗑️ 清理旧视频: {file_path.name}")
                except Exception as e:
                    print(f"清理视频文件失败 {file_path}: {e}")
        
        if deleted_count > 0:
            print(f"[{get_timestamp()}] 🧹 视频清理完成: 删除 {deleted_count} 个文件，释放 {deleted_size // (1024*1024)}MB 空间")
        
    except Exception as e:
        print(f"视频清理过程出错: {e}")

def generate_video_id(original_filename: str = None) -> str:
    """生成简洁的视频ID"""
    timestamp = int(time.time() * 1000)
    random_part = secrets.token_hex(4)
    
    if original_filename:
        # 提取文件扩展名
        ext = Path(original_filename).suffix.lower()
        if ext in ['.mp4', '.webm', '.ogg', '.avi', '.mov', '.wmv', '.flv', '.mkv']:
            return f"{timestamp}_{random_part}{ext}"
    
    # 默认使用 .mp4 扩展名
    return f"{timestamp}_{random_part}.mp4"

def get_video_stats(config: Dict[str, Any]) -> Dict[str, Any]:
    """获取视频存储统计信息"""
    try:
        upload_dir = ensure_video_upload_directory(config)
        
        total_files = 0
        total_size = 0
        
        for file_path in upload_dir.iterdir():
            if file_path.is_file():
                total_files += 1
                total_size += file_path.stat().st_size
        
        video_config = config.get('video_upload', {})
        max_storage = video_config.get('max_storage_size', 5 * 1024 * 1024 * 1024)  # 5GB
        max_files = video_config.get('max_files', 100)
        
        return {
            "total_files": total_files,
            "total_size": total_size,
            "total_size_mb": total_size // (1024 * 1024),
            "max_storage_mb": max_storage // (1024 * 1024),
            "max_files": max_files,
            "storage_usage_percent": (total_size / max_storage * 100) if max_storage > 0 else 0,
            "files_usage_percent": (total_files / max_files * 100) if max_files > 0 else 0
        }
    except Exception as e:
        print(f"获取视频统计信息失败: {e}")
        return {
            "total_files": 0,
            "total_size": 0,
            "total_size_mb": 0,
            "max_storage_mb": 0,
            "max_files": 0,
            "storage_usage_percent": 0,
            "files_usage_percent": 0
        }

def list_videos(config: Dict[str, Any]) -> List[Dict[str, Any]]:
    """列出所有视频文件"""
    try:
        upload_dir = ensure_video_upload_directory(config)
        videos = []
        
        for file_path in upload_dir.iterdir():
            if file_path.is_file():
                try:
                    stat = file_path.stat()
                    mime_type, _ = mimetypes.guess_type(str(file_path))
                    
                    if mime_type and mime_type.startswith('video/'):
                        videos.append({
                            "filename": file_path.name,
                            "size": stat.st_size,
                            "size_mb": stat.st_size // (1024 * 1024),
                            "created_time": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                            "modified_time": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                            "mime_type": mime_type
                        })
                except Exception as e:
                    print(f"读取视频文件信息失败 {file_path}: {e}")
        
        # 按修改时间倒序排列
        videos.sort(key=lambda x: x['modified_time'], reverse=True)
        return videos
        
    except Exception as e:
        print(f"列出视频文件失败: {e}")
        return []

async def upload_video(file: UploadFile, config: Dict[str, Any]) -> Dict[str, Any]:
    """上传视频文件"""
    try:
        # 验证文件类型
        if not file.content_type or not file.content_type.startswith('video/'):
            raise HTTPException(status_code=400, detail="只允许上传视频文件")
        
        # 验证文件大小
        video_config = config.get('video_upload', {})
        max_size = video_config.get('max_file_size', 500 * 1024 * 1024)  # 默认500MB
        
        # 读取文件内容并检查大小
        content = await file.read()
        if len(content) > max_size:
            raise HTTPException(status_code=400, detail=f"文件大小超过限制({max_size // (1024*1024)}MB)")
        
        # 生成视频ID
        video_id = generate_video_id(file.filename)
        
        # 确保上传目录存在
        ensure_video_upload_directory(config)
        
        # 智能存储管理：只在必要时清理
        upload_dir = ensure_video_upload_directory(config)
        try:
            current_size = sum(f.stat().st_size for f in upload_dir.iterdir() if f.is_file())
            max_storage = video_config.get('max_storage_size', 5 * 1024 * 1024 * 1024)  # 5GB
            
            # 只有当存储使用率超过95%时才进行清理
            if current_size + len(content) > max_storage * 0.95:
                print(f"[{get_timestamp()}] ⚠️ 视频存储空间紧张，执行清理...")
                cleanup_old_videos(config)
        except Exception as e:
            print(f"视频存储检查失败，继续上传: {e}")
        
        # 保存到本地
        local_file_path = Path(video_config.get('local_storage_path', 'video_uploads')) / video_id
        
        with open(local_file_path, 'wb') as f:
            f.write(content)
        
        print(f"[{get_timestamp()}] 📹 视频已保存: {video_id} ({len(content) // (1024*1024)}MB)")
        
        # 更新视频统计
        try:
            update_video_stats(config)
            save_stats()
        except Exception as e:
            print(f"更新视频统计失败: {e}")
        
        # 返回视频引用信息
        return {
            "success": True,
            "video_url": f"video_ref:{video_id}",  # 返回视频引用
            "local_path": video_id  # 视频ID
        }
            
    except HTTPException:
        raise
    except Exception as e:
        print(f"[{get_timestamp()}] 视频上传处理异常: {e}")
        raise HTTPException(status_code=500, detail=f"视频上传失败: {str(e)}")

def get_video_file(filename: str, format: str = "file", config: Dict[str, Any] = None):
    """获取视频文件"""
    try:
        from fastapi.responses import FileResponse

        upload_dir = ensure_video_upload_directory(config)
        file_path = upload_dir / filename

        if not file_path.exists() or not file_path.is_file():
            raise HTTPException(status_code=404, detail=f"视频文件不存在: {filename}")

        # 检查文件类型
        mime_type, _ = mimetypes.guess_type(str(file_path))
        if not mime_type or not mime_type.startswith('video/'):
            raise HTTPException(status_code=400, detail="不是有效的视频文件")

        if format == "file":
            # 创建FileResponse，设置为inline显示而不是下载
            response = FileResponse(
                str(file_path),
                media_type=mime_type,
                filename=filename
            )
            # 强制设置为inline，防止下载
            response.headers["Content-Disposition"] = "inline"
            # 设置缓存控制 - 私有缓存，需要重新验证
            response.headers["Cache-Control"] = "private, must-revalidate, max-age=0"
            # 支持范围请求（对视频很重要）
            response.headers["Accept-Ranges"] = "bytes"
            # 添加CORS头
            response.headers["Access-Control-Allow-Origin"] = "*"
            response.headers["Access-Control-Allow-Methods"] = "GET, HEAD, OPTIONS"
            response.headers["Access-Control-Allow-Headers"] = "Authorization, Range"
            return response
        else:
            raise HTTPException(status_code=400, detail="视频文件不支持base64格式")

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取视频失败: {str(e)}")

def delete_video(filename: str, config: Dict[str, Any] = None) -> Dict[str, Any]:
    """删除视频文件"""
    try:
        upload_dir = ensure_video_upload_directory(config)
        file_path = upload_dir / filename
        
        if not file_path.exists():
            raise HTTPException(status_code=404, detail="视频文件不存在")
        
        file_path.unlink()
        
        # 更新视频统计
        try:
            update_video_stats(config)
            save_stats()
        except Exception as e:
            print(f"更新视频统计失败: {e}")
        
        return {"success": True, "message": f"视频 {filename} 已删除"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除视频失败: {str(e)}")

def batch_delete_videos(filenames: List[str], config: Dict[str, Any] = None) -> Dict[str, Any]:
    """批量删除视频文件"""
    try:
        upload_dir = ensure_video_upload_directory(config)
        deleted_files = []
        failed_files = []
        
        for filename in filenames:
            try:
                file_path = upload_dir / filename
                if file_path.exists():
                    file_path.unlink()
                    deleted_files.append(filename)
                else:
                    failed_files.append({"filename": filename, "error": "文件不存在"})
            except Exception as e:
                failed_files.append({"filename": filename, "error": str(e)})
        
        # 更新视频统计
        try:
            update_video_stats(config)
            save_stats()
        except Exception as e:
            print(f"更新视频统计失败: {e}")
        
        return {
            "success": True,
            "deleted_count": len(deleted_files),
            "failed_count": len(failed_files),
            "deleted_files": deleted_files,
            "failed_files": failed_files
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量删除视频失败: {str(e)}")

def manual_cleanup_videos(config: Dict[str, Any] = None) -> Dict[str, Any]:
    """手动清理视频"""
    try:
        cleanup_old_videos(config)
        stats = get_video_stats(config)
        return {
            "success": True,
            "message": "视频清理完成",
            "stats": stats
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"视频清理失败: {str(e)}")
